"use client";

import React, { useMemo, useState } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { Button } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import {
  UserCheck,
  Mail,
  Phone,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Shield,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import { useUsers } from "@/hooks/useUsers";
import {
  CreateEmployeeDialog,
  EditEmployeeDialog,
  DeleteEmployeeDialog,
} from "./dialogs";
import type { User } from "@/lib/api/validators/schemas/user";

export function EmployeeManagement() {
  // Dialog state management
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<User | null>(null);

  // Initialize useUsers hook with employee filter
  const {
    filteredUsers,
    isLoading,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    createEmployee,
    updateEmployee,
    deleteUser,
    refreshUsers,
    isCreating,
    isUpdating,
    isDeleting,
  } = useUsers({
    type: "employee", // Filter for employees only
    page: 1,
    limit: 10,
    includeRole: true, // Include role data for employees
  });

  // Filter only employee users from the data (including admin as they're also employees)
  const employeeUsers = useMemo(() => {
    return filteredUsers.filter(
      (user) => user.type === "employee" || user.type === "admin"
    );
  }, [filteredUsers]);

  // Handle refresh action
  const handleRefresh = () => {
    refreshUsers();
  };

  // Handle create employee
  const handleCreateEmployee = async (employeeData: any) => {
    await createEmployee(employeeData);
  };

  // Handle update employee
  const handleUpdateEmployee = async (id: string, employeeData: any) => {
    await updateEmployee(id, employeeData);
  };

  // Handle delete employee
  const handleDeleteEmployee = async (id: string) => {
    await deleteUser(id);
  };

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setCreateDialogOpen(true);
  };

  const handleOpenEditDialog = (employee: User) => {
    setSelectedEmployee(employee);
    setEditDialogOpen(true);
  };

  const handleOpenDeleteDialog = (employee: User) => {
    setSelectedEmployee(employee);
    setDeleteDialogOpen(true);
  };

  // Employee table columns
  const employeeColumns = [
    {
      key: "user",
      label: "Employee",
      render: (employee: any) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage
              src={employee.image}
              alt={`${employee.firstName} ${employee.lastName}`}
            />
            <AvatarFallback>
              {employee.firstName?.[0]}
              {employee.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">
              {employee.firstName} {employee.lastName}
            </p>
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Mail className="h-3 w-3" />
              {employee.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "role",
      label: "Role",
      render: (employee: any) => (
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-muted-foreground" />
          <span>{employee.role?.name || employee.type}</span>
        </div>
      ),
    },
    {
      key: "contact",
      label: "Contact",
      render: (employee: any) => (
        <div className="flex items-center gap-1 text-sm">
          <Phone className="h-3 w-3 text-muted-foreground" />
          {employee.phone || "No phone"}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (employee: any) => (
        <Badge
          variant={employee.status === "active" ? "default" : "secondary"}
          className="capitalize"
        >
          {employee.status}
        </Badge>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (employee: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleOpenEditDialog(employee)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Employee
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive"
              onClick={() => handleOpenDeleteDialog(employee)}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Employee
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Employee Statistics */}
      <Listing.Statistics columns="grid-cols-3">
        <Listing.StatCard
          name="Total Employees"
          value={employeeUsers.length}
          valueType="number"
          caption="Active employee accounts"
          color="blue"
        />
        <Listing.StatCard
          name="Active Employees"
          value={employeeUsers.filter((e) => e.status === "active").length}
          valueType="number"
          caption="Currently active"
          color="green"
        />
        <Listing.StatCard
          name="With Roles"
          value={employeeUsers.filter((e) => e.roleId).length}
          valueType="number"
          caption="Employees with roles"
          color="primary"
        />
      </Listing.Statistics>

      {/* Employee Management Section */}
      <Listing className="space-y-1">
        <Listing.Header
          title={{ text: "Employee Directory", size: "md" }}
          actions={
            <Button onClick={handleOpenCreateDialog}>
              <Plus size={16} className="mr-2" />
              Add Employee
            </Button>
          }
        />

        {/* Filters */}
        <Listing.Filters
          searchTerm={searchQuery}
          onSearchChange={setSearchQuery}
          onRefresh={handleRefresh}
          loading={isLoading}
          customActions={
            <div className="flex items-center gap-2">
              <Button
                variant={!filters.status ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: undefined })}
              >
                All
              </Button>
              <Button
                variant={filters.status === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: "active" })}
              >
                Active
              </Button>
              <Button
                variant={filters.status === "inactive" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilters({ ...filters, status: "inactive" })}
              >
                Inactive
              </Button>
            </div>
          }
        />

        {/* Employee Table */}
        <Listing.Table
          data={employeeUsers}
          columns={employeeColumns}
          loading={isLoading}
          emptyState={
            <div className="text-center py-8">
              <UserCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchQuery || filters.status
                  ? "No employees match your filters"
                  : "No employees found"}
              </p>
              <Button className="mt-4" onClick={handleOpenCreateDialog}>
                <Plus size={16} className="mr-2" />
                Add Your First Employee
              </Button>
            </div>
          }
        />
      </Listing>

      {/* Dialogs */}
      <CreateEmployeeDialog
        isOpen={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateEmployee}
        isCreating={isCreating}
      />

      <EditEmployeeDialog
        employee={selectedEmployee}
        isOpen={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onSubmit={handleUpdateEmployee}
        isUpdating={isUpdating}
      />

      <DeleteEmployeeDialog
        employee={selectedEmployee}
        isOpen={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteEmployee}
        isDeleting={isDeleting}
      />
    </div>
  );
}
