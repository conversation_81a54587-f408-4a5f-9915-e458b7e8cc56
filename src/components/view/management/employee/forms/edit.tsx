"use client";

import React, { useState, useEffect } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import type {
  User,
  UpdateEmployeeSchema,
} from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Edit Employee Form Props
interface EditEmployeeFormProps {
  onSubmit: (
    id: string,
    data: Partial<z.infer<typeof UpdateEmployeeSchema>>
  ) => Promise<void>;
  employee: User;
  children?: React.ReactNode;
}

// Edit Employee Form Component
export function EditEmployeeForm({
  onSubmit,
  employee,
  children,
}: EditEmployeeFormProps) {
  const [formData, setFormData] = useState<
    Partial<z.infer<typeof UpdateEmployeeSchema>>
  >({});

  // Initialize form data when employee changes
  useEffect(() => {
    if (employee) {
      setFormData({
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        phone: employee.phone || "",
        image: employee.image || "",
        status: employee.status,
        type: employee.type,
        roleId: employee.roleId || "",
      });
    }
  }, [employee]);

  const handleInputChange = (
    field: keyof z.infer<typeof UpdateEmployeeSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.firstName?.trim()) {
      toast.error("First name is required");
      return;
    }

    if (!formData.lastName?.trim()) {
      toast.error("Last name is required");
      return;
    }

    if (!formData.email?.trim()) {
      toast.error("Email is required");
      return;
    }

    if (!formData.roleId?.trim()) {
      toast.error("Role is required for employees");
      return;
    }

    if (formData.password && formData.password !== formData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      await onSubmit(employee.id, formData);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-firstName">First Name *</Label>
            <Input
              id="edit-firstName"
              value={formData.firstName || ""}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              placeholder="Enter first name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-lastName">Last Name *</Label>
            <Input
              id="edit-lastName"
              value={formData.lastName || ""}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              placeholder="Enter last name"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-email">Email *</Label>
          <Input
            id="edit-email"
            type="email"
            value={formData.email || ""}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder="Enter email address"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-phone">Phone</Label>
          <Input
            id="edit-phone"
            value={formData.phone || ""}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            placeholder="Enter phone number"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-image">Profile Image URL</Label>
          <Input
            id="edit-image"
            type="url"
            value={formData.image || ""}
            onChange={(e) => handleInputChange("image", e.target.value)}
            placeholder="Enter profile image URL"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-type">Employee Type</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => handleInputChange("type", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select employee type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="employee">Employee</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-roleId">Role ID *</Label>
          <Input
            id="edit-roleId"
            value={formData.roleId || ""}
            onChange={(e) => handleInputChange("roleId", e.target.value)}
            placeholder="Enter role ID"
            required
          />
        </div>
      </div>

      {/* Password Section */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-password">New Password</Label>
            <Input
              id="edit-password"
              type="password"
              value={formData.password || ""}
              onChange={(e) => handleInputChange("password", e.target.value)}
              placeholder="Enter new password (leave blank to keep current)"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-confirmPassword">Confirm New Password</Label>
            <Input
              id="edit-confirmPassword"
              type="password"
              value={formData.confirmPassword || ""}
              onChange={(e) =>
                handleInputChange("confirmPassword", e.target.value)
              }
              placeholder="Confirm new password"
            />
          </div>
        </div>
      </div>

      {children}
    </form>
  );
}
