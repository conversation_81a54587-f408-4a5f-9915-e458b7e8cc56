"use client";

import React, { useState } from "react";
import { Input } from "@/components/common/ui/input";
import { Label } from "@/components/common/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/ui/select";
import { toast } from "sonner";
import type { CreateClientSchema } from "@/lib/api/validators/schemas/user";
import { z } from "zod";

// Create Client Form Props
interface CreateClientFormProps {
  children?: React.ReactNode;
  onSubmit: (data: z.infer<typeof CreateClientSchema>) => Promise<void>;
}

// Initial form data for create
const INITIAL_CREATE_FORM_DATA: z.infer<typeof CreateClientSchema> = {
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  image: "",
  status: "created",
  type: "client",
  companyId: "",
  subscriptionId: "",
  password: "",
  confirmPassword: "",
};

// Create Client Form Component
export function CreateClientForm({
  children,
  onSubmit,
}: CreateClientFormProps) {
  const [formData, setFormData] = useState<z.infer<typeof CreateClientSchema>>(
    INITIAL_CREATE_FORM_DATA
  );

  const handleInputChange = (
    field: keyof z.infer<typeof CreateClientSchema>,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.firstName.trim()) {
      toast.error("First name is required");
      return;
    }

    if (!formData.lastName.trim()) {
      toast.error("Last name is required");
      return;
    }

    if (!formData.email.trim()) {
      toast.error("Email is required");
      return;
    }

    if (formData.password && formData.password !== formData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      await onSubmit(formData);
      // Reset form after successful submission
      setFormData(INITIAL_CREATE_FORM_DATA);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              placeholder="Enter first name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              placeholder="Enter last name"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder="Enter email address"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={formData.phone || ""}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            placeholder="Enter phone number"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="image">Profile Image URL</Label>
          <Input
            id="image"
            type="url"
            value={formData.image || ""}
            onChange={(e) => handleInputChange("image", e.target.value)}
            placeholder="Enter profile image URL"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select
            value={formData.status}
            onValueChange={(value) => handleInputChange("status", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created">Created</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="companyId">Company ID</Label>
            <Input
              id="companyId"
              value={formData.companyId || ""}
              onChange={(e) => handleInputChange("companyId", e.target.value)}
              placeholder="Enter company ID"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="subscriptionId">Subscription ID</Label>
            <Input
              id="subscriptionId"
              value={formData.subscriptionId || ""}
              onChange={(e) =>
                handleInputChange("subscriptionId", e.target.value)
              }
              placeholder="Enter subscription ID"
            />
          </div>
        </div>
      </div>

      {/* Password Section */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password || ""}
              onChange={(e) => handleInputChange("password", e.target.value)}
              placeholder="Enter password"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={formData.confirmPassword || ""}
              onChange={(e) =>
                handleInputChange("confirmPassword", e.target.value)
              }
              placeholder="Confirm password"
            />
          </div>
        </div>
      </div>

      {children}
    </form>
  );
}
