"use client";

import React, { useState } from "react";
import { Listing } from "@/layouts/dashboard/details/basic";
import { But<PERSON> } from "@/components/common/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/common/ui/tabs";
import { Users, UserCheck, Plus } from "lucide-react";
import { ClientManagement } from "./client";
import { EmployeeManagement } from "./employee";

export function UserManagementContainer() {
  const [activeTab, setActiveTab] = useState("clients");

  return (
    <Listing className="p-6 space-y-2">
      {/* Dashboard Header */}
      <Listing.Header
        title={{ text: "User Management", size: "2xl" }}
        caption="Manage employees and clients across your organization"
      />

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-max grid-cols-2">
          <TabsTrigger value="clients" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Clients
          </TabsTrigger>
          <TabsTrigger value="employees" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            Employees
          </TabsTrigger>
        </TabsList>

        <TabsContent value="clients" className="mt-6">
          <ClientManagement />
        </TabsContent>

        <TabsContent value="employees" className="mt-6">
          <EmployeeManagement />
        </TabsContent>
      </Tabs>
    </Listing>
  );
}
