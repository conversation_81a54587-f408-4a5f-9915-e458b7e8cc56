import { BaseService, ServiceResponse, ServiceContext } from "./base";
import { prisma } from "@/lib/common/prisma";
import { NotificationService } from "./notification";
import { CryptoMiddleware } from "@/lib/crypto/middleware";
import {
  CreateUserSchema,
  CreateClientSchema,
  CreateEmployeeSchema,
  UpdateUserSchema,
  UpdateClientSchema,
  UpdateEmployeeSchema,
  UserQuerySchema,
  BulkUserSchema,
  UserStatisticsSchema,
  type CreateUser,
  type CreateClient,
  type CreateEmployee,
  type UpdateUser,
  type UpdateClient,
  type UpdateEmployee,
  type UserQuery,
  type BulkUser,
  type User,
  type UserStatistics,
} from "@/lib/api/validators/schemas/user";
import bcrypt from "bcryptjs";

/**
 * User Service
 *
 * Handles all user-related operations including CRUD operations for both
 * clients and employees with differentiated contexts, search, statistics,
 * and bulk actions with notification integration.
 */
export class UserService extends BaseService {
  private notificationService: NotificationService;

  constructor() {
    super({
      enableLogging: true,
      throwOnError: false,
      validateInput: true,
      validateOutput: false,
    });

    // Set the Prisma model for user operations
    this.setModel(prisma.user as any);

    // Initialize notification service
    this.notificationService = new NotificationService();
  }

  /**
   * Set service context for request handling
   */
  setContext(context: ServiceContext) {
    super.setContext(context);
    this.notificationService.setContext(context);
  }

  /**
   * Create a new client user
   */
  async createClient(clientData: CreateClient): Promise<ServiceResponse<User>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(CreateClientSchema, clientData);

      // Check if user with same email already exists
      const existingUser = await this.findFirstRecord({
        where: { email: validatedData.email },
      });

      if (existingUser) {
        throw new Error("User with this email already exists");
      }

      let lastIndex = await this.countRecords({ where: { type: "client" } });
      let nextIndex = lastIndex + 1;

      let defaultPassword: string = `welcome@TenderBank#${nextIndex}`;
      let hashedPassword = CryptoMiddleware.hashPassword(defaultPassword);

      // Prepare client data
      const { password, confirmPassword, ...clientDataWithoutPassword } =
        validatedData;

      // Create the client user
      const client = await this.createRecord<User>({
        data: {
          ...clientDataWithoutPassword,
          type: "client",
          // Create account if password provided
          accounts: {
            create: {
              type: "credentials",
              provider: "credentials",
              providerAccountId: validatedData.email,
              password: hashedPassword,
            },
          },
        },
        include: {
          company: true,
          role: true,
        },
      });

      // Send notification for new client creation
      if (this.context?.user) {
        await this.notificationService.createNotification({
          userId: this.context.user.id,
          title: "New Client Created",
          message: `Client ${client.firstName} ${client.lastName} has been created successfully`,
          type: "systemAlerts",
          category: "inApp",
          priority: "normal",
          data: {
            clientId: client.id,
            clientName: `${client.firstName} ${client.lastName}`,
            clientEmail: client.email,
            action: "created",
          },
        });
      }

      return client;
    }, "createClient");
  }

  /**
   * Create a new employee user
   */
  async createEmployee(
    employeeData: CreateEmployee
  ): Promise<ServiceResponse<User>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(
        CreateEmployeeSchema,
        employeeData
      );

      // Check if user with same email already exists
      const existingUser = await this.findFirstRecord({
        where: { email: validatedData.email },
      });

      if (existingUser) {
        throw new Error("User with this email already exists");
      }

      // Verify role exists
      const role = await prisma.role.findUnique({
        where: { id: validatedData.roleId },
      });

      if (!role) {
        throw new Error("Invalid role specified");
      }

      let lastIndex = await this.countRecords({ where: { type: "employee" } });
      let nextIndex = lastIndex + 1;

      let defaultPassword: string = `user@Tenders#${nextIndex}`;
      let hashedPassword = CryptoMiddleware.hashPassword(defaultPassword);

      // Prepare employee data
      const { password, confirmPassword, ...employeeDataWithoutPassword } =
        validatedData;

      // Create the employee user
      const employee = await this.createRecord<User>({
        data: {
          ...employeeDataWithoutPassword,
          // Create account if password provided
          accounts: {
            create: {
              type: "credentials",
              provider: "credentials",
              providerAccountId: validatedData.email,
              password: hashedPassword,
            },
          },
        },
        include: {
          role: true,
        },
      });

      // Send notification for new employee creation
      if (this.context?.user) {
        await this.notificationService.createNotification({
          userId: this.context.user.id,
          title: "New Employee Created",
          message: `Employee ${employee.firstName} ${employee.lastName} has been created successfully`,
          type: "systemAlerts",
          category: "inApp",
          priority: "normal",
          data: {
            employeeId: employee.id,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeEmail: employee.email,
            action: "created",
          },
        });
      }

      return employee;
    }, "createEmployee");
  }

  /**
   * Update a client user
   */
  async updateClient(
    id: string,
    clientData: Partial<UpdateClient>
  ): Promise<ServiceResponse<User>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(UpdateClientSchema.partial(), {
        id,
        ...clientData,
      });

      // Check if user exists and is a client
      const existingUser = (await this.findUniqueRecord({
        where: { id },
      })) as any;

      if (!existingUser) {
        throw new Error("User not found");
      }

      if (existingUser.type !== "client") {
        throw new Error("User is not a client");
      }

      // Check email uniqueness if email is being updated
      if (validatedData.email && validatedData.email !== existingUser.email) {
        const emailExists = await this.findFirstRecord({
          where: {
            email: validatedData.email,
            id: { not: id },
          },
        });

        if (emailExists) {
          throw new Error("Email already exists");
        }
      }

      // Handle password update if provided
      let updateData = { ...validatedData };
      if (validatedData.password) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(validatedData.password, salt);

        // Update account password
        await prisma.account.updateMany({
          where: { userId: id },
          data: { password: hashedPassword },
        });
      }

      // Remove password fields from update data
      delete updateData.password;
      delete updateData.confirmPassword;
      delete updateData.id;

      // Update the client
      const updatedClient = (await this.updateRecord({
        where: { id },
        data: updateData,
        include: {
          company: true,
          role: true,
        },
      })) as User;

      return updatedClient;
    }, "updateClient");
  }

  /**
   * Update an employee user
   */
  async updateEmployee(
    id: string,
    employeeData: Partial<UpdateEmployee>
  ): Promise<ServiceResponse<User>> {
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(UpdateEmployeeSchema.partial(), {
        id,
        ...employeeData,
      });

      // Check if user exists and is an employee/admin
      const existingUser = (await this.findUniqueRecord({
        where: { id },
      })) as any;

      if (!existingUser) {
        throw new Error("User not found");
      }

      if (!["admin", "employee"].includes(existingUser.type)) {
        throw new Error("User is not an employee or admin");
      }

      // Check email uniqueness if email is being updated
      if (validatedData.email && validatedData.email !== existingUser.email) {
        const emailExists = await this.findFirstRecord({
          where: {
            email: validatedData.email,
            id: { not: id },
          },
        });

        if (emailExists) {
          throw new Error("Email already exists");
        }
      }

      // Verify role exists if roleId is being updated
      if (validatedData.roleId) {
        const role = await prisma.role.findUnique({
          where: { id: validatedData.roleId },
        });

        if (!role) {
          throw new Error("Invalid role specified");
        }
      }

      // Handle password update if provided
      let updateData = { ...validatedData };
      if (validatedData.password) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(validatedData.password, salt);

        // Update account password
        await prisma.account.updateMany({
          where: { userId: id },
          data: { password: hashedPassword },
        });
      }

      // Remove password fields from update data
      delete updateData.password;
      delete updateData.confirmPassword;
      delete updateData.id;

      // Update the employee
      const updatedEmployee = (await this.updateRecord({
        where: { id },
        data: updateData,
        include: {
          role: true,
        },
      })) as User;

      return updatedEmployee;
    }, "updateEmployee");
  }

  /**
   * Generic update user method
   */
  async updateUser(
    id: string,
    userData: Partial<UpdateUser>
  ): Promise<ServiceResponse<User>> {
    // Check if user exists first to determine type
    const existingUser = (await this.findUniqueRecord({
      where: { id },
    })) as any;

    if (!existingUser) {
      return this.createErrorResponse("User not found", 404);
    }

    // Route to specific update method based on user type
    if (existingUser.type === "client") {
      return this.updateClient(id, userData as Partial<UpdateClient>);
    } else if (["admin", "employee"].includes(existingUser.type)) {
      return this.updateEmployee(id, userData as Partial<UpdateEmployee>);
    }

    // Fallback for generic updates
    return this.executeOperation(async () => {
      // Validate input data
      const validatedData = this.validateInput(UpdateUserSchema.partial(), {
        id,
        ...userData,
      });

      let updateData = { ...validatedData };
      delete updateData.id;

      const updatedUser = (await this.updateRecord({
        where: { id },
        data: updateData,
        include: {
          company: true,
          role: true,
        },
      })) as User;

      return updatedUser;
    }, "updateUser");
  }

  /**
   * Delete a user
   */
  async deleteUser(id: string): Promise<ServiceResponse<User>> {
    return this.executeOperation(async () => {
      // Check if user exists
      const existingUser = (await this.findUniqueRecord({
        where: { id },
      })) as any;

      if (!existingUser) {
        throw new Error("User not found");
      }

      // Delete the user (this will cascade delete related records)
      const deletedUser = (await this.deleteRecord({
        where: { id },
      })) as User;

      // Send notification for user deletion
      if (this.context?.user) {
        await this.notificationService.createNotification({
          userId: this.context.user.id,
          title: "User Deleted",
          message: `User ${existingUser.firstName} ${existingUser.lastName} has been deleted`,
          type: "systemAlerts",
          category: "inApp",
          priority: "normal",
          data: {
            deletedUserId: id,
            deletedUserName: `${existingUser.firstName} ${existingUser.lastName}`,
            deletedUserEmail: existingUser.email,
            deletedUserType: existingUser.type,
            action: "deleted",
          },
        });
      }

      return deletedUser;
    }, "deleteUser");
  }

  /**
   * Search users with filtering and pagination
   */
  async searchUsers(queryParams: UserQuery): Promise<ServiceResponse<User[]>> {
    return this.executeOperation(
      async () => {
        // Validate query parameters
        const validatedParams = this.validateInput(
          UserQuerySchema,
          queryParams
        );

        // Build where clause
        const where: any = {};

        if (validatedParams.id) {
          where.id = validatedParams.id;
        }

        if (validatedParams.firstName) {
          where.firstName = {
            contains: validatedParams.firstName,
            mode: "insensitive",
          };
        }

        if (validatedParams.lastName) {
          where.lastName = {
            contains: validatedParams.lastName,
            mode: "insensitive",
          };
        }

        if (validatedParams.email) {
          where.email = {
            contains: validatedParams.email,
            mode: "insensitive",
          };
        }

        if (validatedParams.type) {
          where.type = validatedParams.type;
        }

        if (validatedParams.status) {
          where.status = validatedParams.status;
        }

        if (validatedParams.companyId) {
          where.companyId = validatedParams.companyId;
        }

        if (validatedParams.roleId) {
          where.roleId = validatedParams.roleId;
        }

        if (validatedParams.search) {
          where.OR = [
            {
              firstName: {
                contains: validatedParams.search,
                mode: "insensitive",
              },
            },
            {
              lastName: {
                contains: validatedParams.search,
                mode: "insensitive",
              },
            },
            {
              email: { contains: validatedParams.search, mode: "insensitive" },
            },
          ];
        }

        // Build include clause
        const include: any = {};
        if (validatedParams.includeRole) {
          include.role = true;
        }
        if (validatedParams.includeCompany) {
          include.company = true;
        }

        // Set pagination
        const paginationSettings = {
          page: validatedParams.page,
          limit: validatedParams.limit,
        };
        this.handlePagination(paginationSettings);

        // Get users with pagination
        const users = await this.findManyRecords<User>({
          where,
          include,
          orderBy: { [validatedParams.sortBy]: validatedParams.sortOrder },
          take: this.limit,
          skip: this.offset,
        });

        return users;
      },
      "searchUsers",
      true
    );
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(): Promise<ServiceResponse<UserStatistics>> {
    return this.executeOperation(async () => {
      // Get total counts
      const totalUsers = await prisma.user.count();
      const activeUsers = await prisma.user.count({
        where: { status: "active" },
      });
      const inactiveUsers = await prisma.user.count({
        where: { status: "inactive" },
      });

      // Get counts by type
      const adminUsers = await prisma.user.count({
        where: { type: "admin" },
      });
      const employeeUsers = await prisma.user.count({
        where: { type: "employee" },
      });
      const clientUsers = await prisma.user.count({
        where: { type: "client" },
      });

      // Get users with companies and roles
      const usersWithCompanies = await prisma.user.count({
        where: { companyId: { not: null } },
      });
      const usersWithRoles = await prisma.user.count({
        where: { roleId: { not: null } },
      });

      // Get recent users
      const recentUsers = await prisma.user.findMany({
        take: 10,
        orderBy: { createdAt: "desc" },
        include: {
          role: true,
          company: true,
        },
      });

      // Build statistics object
      const statistics: UserStatistics = {
        totalUsers,
        activeUsers,
        inactiveUsers,
        adminUsers,
        employeeUsers,
        clientUsers,
        usersByStatus: {
          active: activeUsers,
          inactive: inactiveUsers,
        },
        usersByType: {
          admin: adminUsers,
          employee: employeeUsers,
          client: clientUsers,
        },
        recentUsers: recentUsers as User[],
        usersWithCompanies,
        usersWithRoles,
      };

      return statistics;
    }, "getUserStatistics");
  }
}
