"use client";

import { useCallback, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import useSWR from "swr";
import { useSearch, type SearchConfig } from "@/lib/search";

import { api } from "@/lib/common/requests";
import {
  createClient,
  createEmployee,
  updateClient,
  updateEmployee,
  updateUser,
  deleteUser,
  performBulkUserAction,
  getUserStatistics,
  type UserSearchParams,
} from "@/store/actions/user";
import type {
  User,
  UserType,
  CreateClient,
  CreateEmployee,
  UpdateClient,
  UpdateEmployee,
  UpdateUser,
  BulkUser,
  UserStatistics,
} from "@/lib/api/validators/schemas/user";

// Hook interface
export interface UseUsersReturn {
  // Data (from SWR)
  users: User[];
  filteredUsers: User[];
  currentUser: User | null;
  statistics: UserStatistics | null;

  // Loading states (from SWR + local state)
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkActionLoading: boolean;
  isStatisticsLoading: boolean;

  // Error handling (from SWR)
  error: string | null;

  // Search and filtering (local state)
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filters: {
    type?: UserType;
    status?: string;
    companyId?: string;
    roleId?: string;
  };
  setFilters: (filters: any) => void;
  clearFilters: () => void;

  // Pagination (local state)
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  setPagination: (pagination: any) => void;

  // Selection (local state)
  selectedUserIds: string[];
  selectedUsers: User[];
  setSelectedUserIds: (ids: string[]) => void;
  toggleUserSelection: (id: string) => void;
  clearSelection: () => void;

  // CRUD operations (Redux actions + SWR revalidation)
  createClient: (data: CreateClient) => Promise<void>;
  createEmployee: (data: CreateEmployee) => Promise<void>;
  updateClient: (id: string, data: Partial<UpdateClient>) => Promise<void>;
  updateEmployee: (id: string, data: Partial<UpdateEmployee>) => Promise<void>;
  updateUser: (id: string, data: Partial<UpdateUser>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  performBulkAction: (data: BulkUser) => Promise<void>;

  // Data fetching (SWR)
  refreshUsers: () => void;
  getUserStatistics: () => Promise<void>;
  setCurrentUser: (user: User | null) => void;
  mutateUsers: () => void;
}

// SWR fetcher function
const fetcher = async (url: string) => {
  const response = await api.get(url);
  return response;
};

// Main hook
export const useUsers = (initialParams?: UserSearchParams): UseUsersReturn => {
  const dispatch = useDispatch();

  // Local state (replacing Redux state)
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState(initialParams?.search || "");
  const [filters, setFilters] = useState({
    type: initialParams?.type,
    status: initialParams?.status,
    companyId: initialParams?.companyId,
    roleId: initialParams?.roleId,
  });
  const [pagination, setPagination] = useState({
    page: initialParams?.page || 1,
    limit: initialParams?.limit || 10,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);

  // CRUD operation loading states
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isBulkActionLoading, setIsBulkActionLoading] = useState(false);
  const [isStatisticsLoading, setIsStatisticsLoading] = useState(false);

  // SWR for data fetching
  const buildSWRKey = useCallback(() => {
    const params = new URLSearchParams();
    if (searchQuery) params.append("search", searchQuery);
    if (filters.type) params.append("type", filters.type);
    if (filters.status) params.append("status", filters.status);
    if (filters.companyId) params.append("companyId", filters.companyId);
    if (filters.roleId) params.append("roleId", filters.roleId);
    params.append("page", pagination.page.toString());
    params.append("limit", pagination.limit.toString());
    return `user?${params.toString()}`;
  }, [searchQuery, filters, pagination.page, pagination.limit]);

  const {
    data: swrUsers,
    error: swrError,
    mutate: mutateUsers,
    isLoading: swrLoading,
  } = useSWR(buildSWRKey(), fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000,
  });

  // Search configuration for the search library
  const searchConfig: SearchConfig<User, User> = {
    // Data transformation (identity function since API and UI data are the same)
    adaptApiToUI: (apiData: User) => apiData,

    // Define searchable fields
    searchFields: (item: User) => [
      item.firstName || "",
      item.lastName || "",
      item.email,
      item.type,
      item.status,
      item.phone || "",
    ],

    // State setters
    setFilteredData: setFilteredUsers,
    setSearchTerm: setSearchQuery,

    // Search action (fallback to Redux action for server-side search)
    searchAction: async (_params: any) => {
      // This would be used for server-side search if needed
      // For now, we'll rely on local search with SWR data
      return { data: [], pagination: pagination };
    },

    // Debounce settings
    debounceDelay: 300,
  };

  // Initialize search hook
  const performSearch = useSearch(dispatch, searchConfig);

  // Computed values
  const selectedUsers = filteredUsers.filter((user) =>
    selectedUserIds.includes(user.id)
  );

  // Perform search when SWR data changes
  useEffect(() => {
    if (swrUsers?.data) {
      performSearch({ query: searchQuery });
    }
  }, [swrUsers?.data, searchQuery, performSearch]);

  // Update pagination when SWR data changes
  useEffect(() => {
    if (swrUsers?.pagination) {
      setPagination((prev) => ({ ...prev, ...swrUsers.pagination }));
    }
  }, [swrUsers?.pagination]);

  // Helper functions for selection
  const toggleUserSelection = useCallback((id: string) => {
    setSelectedUserIds((prev) => {
      const index = prev.indexOf(id);
      if (index > -1) {
        return prev.filter((userId) => userId !== id);
      } else {
        return [...prev, id];
      }
    });
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedUserIds([]);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      type: undefined,
      status: undefined,
      companyId: undefined,
      roleId: undefined,
    });
    setSearchQuery("");
  }, []);

  // CRUD operation handlers
  const handleCreateClient = useCallback(
    async (data: CreateClient) => {
      setIsCreating(true);
      try {
        await dispatch(createClient(data) as any);
        mutateUsers();
      } finally {
        setIsCreating(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handleCreateEmployee = useCallback(
    async (data: CreateEmployee) => {
      setIsCreating(true);
      try {
        await dispatch(createEmployee(data) as any);
        mutateUsers();
      } finally {
        setIsCreating(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handleUpdateClient = useCallback(
    async (id: string, data: Partial<UpdateClient>) => {
      setIsUpdating(true);
      try {
        await dispatch(updateClient({ id, data }) as any);
        mutateUsers();
      } finally {
        setIsUpdating(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handleUpdateEmployee = useCallback(
    async (id: string, data: Partial<UpdateEmployee>) => {
      setIsUpdating(true);
      try {
        await dispatch(updateEmployee({ id, data }) as any);
        mutateUsers();
      } finally {
        setIsUpdating(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handleUpdateUser = useCallback(
    async (id: string, data: Partial<UpdateUser>) => {
      setIsUpdating(true);
      try {
        await dispatch(updateUser({ id, data }) as any);
        mutateUsers();
      } finally {
        setIsUpdating(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handleDeleteUser = useCallback(
    async (id: string) => {
      setIsDeleting(true);
      try {
        await dispatch(deleteUser(id) as any);
        mutateUsers();
      } finally {
        setIsDeleting(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handlePerformBulkAction = useCallback(
    async (data: BulkUser) => {
      setIsBulkActionLoading(true);
      try {
        await dispatch(performBulkUserAction(data) as any);
        mutateUsers();
      } finally {
        setIsBulkActionLoading(false);
      }
    },
    [dispatch, mutateUsers]
  );

  const handleGetUserStatistics = useCallback(async () => {
    setIsStatisticsLoading(true);
    try {
      const result = await dispatch(getUserStatistics() as any);
      if (result.payload) {
        setStatistics(result.payload);
      }
    } finally {
      setIsStatisticsLoading(false);
    }
  }, [dispatch]);

  const handleRefreshUsers = useCallback(() => {
    mutateUsers();
  }, [mutateUsers]);

  return {
    // Data (from SWR)
    users: swrUsers?.data || [],
    filteredUsers,
    currentUser,
    statistics,

    // Loading states (from SWR + local state)
    isLoading: swrLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isBulkActionLoading,
    isStatisticsLoading,

    // Error handling (from SWR)
    error: swrError?.message || null,

    // Search and filtering (local state)
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    clearFilters,

    // Pagination (local state)
    pagination,
    setPagination,

    // Selection (local state)
    selectedUserIds,
    selectedUsers,
    setSelectedUserIds,
    toggleUserSelection,
    clearSelection,

    // CRUD operations (Redux actions + SWR revalidation)
    createClient: handleCreateClient,
    createEmployee: handleCreateEmployee,
    updateClient: handleUpdateClient,
    updateEmployee: handleUpdateEmployee,
    updateUser: handleUpdateUser,
    deleteUser: handleDeleteUser,
    performBulkAction: handlePerformBulkAction,

    // Data fetching (SWR)
    refreshUsers: handleRefreshUsers,
    getUserStatistics: handleGetUserStatistics,
    setCurrentUser,
    mutateUsers,
  };
};
